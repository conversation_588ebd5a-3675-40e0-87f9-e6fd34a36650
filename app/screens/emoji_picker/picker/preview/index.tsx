// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback} from 'react';
import {View, Text, TouchableOpacity, ScrollView, StyleSheet} from 'react-native';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    onDone: () => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderBottomWidth: 1,
            borderBottomColor: changeOpacity(theme.centerChannelColor, 0.08),
            paddingHorizontal: 16,
            paddingVertical: 12,
            maxHeight: 100,
            minHeight: 60, // Ensure consistent height
        },
        header: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 8,
        },
        title: {
            fontSize: 14,
            fontWeight: '600',
            color: theme.centerChannelColor,
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 6,
            borderRadius: 16,
        },
        doneButtonText: {
            color: theme.buttonColor,
            fontSize: 14,
            fontWeight: '600',
        },
        scrollContainer: {
            flex: 1,
            paddingRight: 16, // Add padding to ensure last item is visible
        },
        scrollContent: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 4,
            paddingHorizontal: 0, // Remove horizontal padding from content
            minWidth: '100%', // Ensure content takes full width when few items
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.6),
            fontSize: 12,
            fontStyle: 'italic',
            textAlign: 'center',
            flex: 1,
            paddingVertical: 8,
        },
        emojiItem: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: changeOpacity(theme.buttonBg, 0.12),
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            marginRight: 8, // Increased margin for better spacing
            borderWidth: 1,
            borderColor: changeOpacity(theme.buttonBg, 0.25),
            minWidth: 50, // Ensure minimum width for touch targets
            flexShrink: 0, // Prevent items from shrinking
        },
        emojiCharacter: {
            fontSize: 16,
            marginRight: 4,
        },
        removeButton: {
            padding: 2,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.1),
            minWidth: 20,
            minHeight: 20,
            alignItems: 'center',
            justifyContent: 'center',
        },
        removeIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.7),
        },
    });
});

const EmojiPickerPreview = ({selectedEmojis, onRemoveEmoji, onDone, testID = 'emoji_picker_preview'}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);

    const handleRemoveEmoji = useCallback((id: string) => {
        onRemoveEmoji(id);
    }, [onRemoveEmoji]);

    const handleDone = useCallback(() => {
        onDone();
    }, [onDone]);

    if (selectedEmojis.length === 0) {
        return (
            <View style={styles.container} testID={`${testID}.container`}>
                <View style={styles.header}>
                    <Text style={styles.title}>
                        Selected (0)
                    </Text>
                </View>
                <Text style={styles.emptyText}>
                    Tap emojis to select them
                </Text>
            </View>
        );
    }

    return (
        <View style={styles.container} testID={`${testID}.container`}>
            <View style={styles.header}>
                <Text style={styles.title}>
                    Selected ({selectedEmojis.length})
                </Text>
                <TouchableOpacity
                    style={styles.doneButton}
                    onPress={handleDone}
                    testID={`${testID}.done_button`}
                >
                    <Text style={styles.doneButtonText}>
                        Done
                    </Text>
                </TouchableOpacity>
            </View>
            <View style={styles.scrollContainer}>
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.scrollContent}
                    testID={`${testID}.scroll_view`}
                    bounces={true}
                    scrollEventThrottle={16}
                    decelerationRate="fast"
                    removeClippedSubviews={false}
                    keyboardShouldPersistTaps="handled"
                    nestedScrollEnabled={true}
                    directionalLockEnabled={true}
                >
                    {selectedEmojis.map((emoji, index) => (
                        <View
                            key={emoji.id}
                            style={[
                                styles.emojiItem,
                                index === selectedEmojis.length - 1 && { marginRight: 16 } // Extra margin for last item
                            ]}
                            testID={`${testID}.emoji_item.${emoji.id}`}
                        >
                            <Text style={styles.emojiCharacter}>
                                {emoji.character}
                            </Text>
                            <TouchableOpacity
                                style={styles.removeButton}
                                onPress={() => handleRemoveEmoji(emoji.id)}
                                testID={`${testID}.remove_button.${emoji.id}`}
                                hitSlop={{top: 8, bottom: 8, left: 8, right: 8}}
                                activeOpacity={0.7}
                            >
                                <CompassIcon
                                    name='close'
                                    size={10}
                                    style={styles.removeIcon}
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            </View>
        </View>
    );
};

export default EmojiPickerPreview;
